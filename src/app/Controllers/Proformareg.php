<?php
 
namespace App\Controllers;
 
use CodeIgniter\RESTful\ResourceController;
use App\Models\LogonModel;
 
class Proformareg extends ResourceController
{
    protected $modelName = 'App\Models\ProformaregModel';
    protected $format    = 'json';

    protected $logonModel;
    protected $session;
    public function __construct(){
        $this->session = \Config\Services::session();

        $this->logonModel = new LogonModel();
    }
 
    /**
     * Return an array of resource objects, themselves in array format
     *
     * @return mixed
     */
    public function index()
    {
        try {
            $this->logonModel->setDBYear($this->session->get('year'));
            return $this->respond($this->model->findAll(), 200);
        } catch (\Exception $e) {
            echo "An error occurred: ". $e->getMessage();
            return $this->fail(['error' => 'error'], 500);
        }
    }
 
    /**
     * Return the properties of a resource object
     *
     * @return mixed
     */
    public function show($id = null)
    {
        try {
            $this->logonModel->setDBYear($this->session->get('year'));
            $data = $this->model->find($id);
            if(is_null($data)) {
                return $this->fail(['error' => 'Proformareg does not exist'], 404);
            }
    
            return $this->respond($data,200);
        } catch (\Exception $e) {
            echo "An error occurred: ". $e->getMessage();
            return $this->fail(['error' => 'error'], 500);
        }
    }
 
     
    /**
     * Create a new resource object, from "posted" parameters
     *
     * @return mixed
     */
    public function create()
    {   
        try {
            $this->logonModel->setDBYear($this->session->get('year'));
            $jsonData = $this->request->getJSON();
            $data = [];
            $allowedFields = [
                'date', 'proformanum', 'total', 'paid', 'premark', 'type', 'status', 'servicetaxtype', 'checkval', 'tax', 'discount', 'company'
            ];

            foreach ($jsonData as $key => $value) {
                if (in_array($key, $allowedFields)) {
                    $data[$key] = $value;
                }
            }
            
            $createdRow = $this->model->insert($data);
            
            return $this->respond(['id' => $createdRow],201);
        } catch (\Exception $e) {
            echo "An error occurred: ". $e->getMessage();
            return $this->fail(['error' => 'error'], 500);
        }
    }
 
    /**
     * Add or update a model resource, from "posted" properties
     *
     * @return mixed
     */
    public function update($id = null)
    {   
        try {
            $this->logonModel->setDBYear($this->session->get('year'));
            $jsonData = $this->request->getJSON();
            $data = [];
            $allowedFields = [
                'date', 'proformanum', 'total', 'paid', 'premark', 'type', 'status', 'servicetaxtype', 'checkval', 'tax', 'discount', 'company'
            ];

            foreach ($jsonData as $key => $value) {
                if (in_array($key, $allowedFields)) {
                    $data[$key] = $value;
                }
            }
    
            if ($this->model->where('id', $id)->set($data)->update() === false)
            {
                $response = [
                    'errors' => $this->model->errors(),
                    'message' => 'Invalid Inputs'
                ];
                return $this->fail($response , 409);
            }
    
            return $this->respond(['message' => 'Updated Successfully'], 200);
        } catch (\Exception $e) {
            echo "An error occurred: ". $e->getMessage();
            return $this->fail(['error' => 'error'], 500);
        }
    }
 
    /**
     * Delete the designated resource object from the model
     *
     * @return mixed
     */
    public function delete($id = null)
    {
        try {
            $this->logonModel->setDBYear($this->session->get('year'));
            $this->model->delete($id);
            return $this->respond(['message' => 'Deleted Successfully'], 200);
        } catch (\Exception $e) {
            echo "An error occurred: ". $e->getMessage();
            return $this->fail(['error' => 'error'], 500);
        }
    }
}
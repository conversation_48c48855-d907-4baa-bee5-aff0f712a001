<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddCompanyToProformareg extends Migration
{
    public function up()
    {
        $fields = [
            'company' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'null'       => true,
                'after'      => 'date',
            ],
        ];

        $this->forge->addColumn('proformareg', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('proformareg', 'company');
    }
}

<?php

namespace App\Models;

use CodeIgniter\Model;

class ProformaregModel extends Model
{
    protected $table = 'proformareg';
    protected $primaryKey = 'id';
    protected $returnType = 'array';
    protected $allowedFields = ['date', 'proformanum', 'total', 'paid', 'premark', 'type', 'status', 'servicetaxtype', 'checkval', 'tax', 'discount', 'company'];
    protected $useSoftDeletes = false;
    protected $useTimestamps = false;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // You can add custom methods here for specific operations
}
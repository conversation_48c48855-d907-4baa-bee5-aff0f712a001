export interface IProforma {
  id: string;
  hid: string;
  mintype: string;
  kms: string;
  xkms: string;
  rate: string;
  tot1: string;
  xhrs: string;
  wchr: string;
  tot2: string;
  early: string;
  early_morning: string;
  late: string;
  onite: string;
  toll: string;
  ptoll: string;
  tot3: string;
  mode: string;
  billed: string;
  tid: string;
  selectedmode: string;
  totaladditionalchar: string;
  totaladditionalparticulars: string;
}

export interface IProformaCreate {
  id?: string;
  date: string;
  company: string;
  status: 'pending' | 'completed';
  sr?: number;
}

export interface IProformaBill {
  id: string;
  proformaId: string;
  billNumber: string;
  date: string;
  company: string;
  total: string;
  status: string;
}

export interface IReadProforma {
  id: string;
}

/* eslint-disable no-useless-escape */
import { useEffect, useRef, useState } from 'react';

import parse from 'html-react-parser';
import { useReactToPrint } from 'react-to-print';
import { ToWords } from 'to-words';

import { Button, Grid, Paper } from '@mui/material';

import PageTitle from '@components/PageTitle';

import { formatDate } from '../../helpers/dateFormat';
import { useReadCompanies } from '../../hooks/company';

const ProformaView = () => {
  const toWords = new ToWords({
    localeCode: 'en-IN',
    converterOptions: {
      currency: true,
      ignoreDecimal: false,
      ignoreZeroCurrency: false,
      doNotAddOnly: false,
      currencyOptions: {
        name: 'Rupee',
        plural: 'Rupees',
        symbol: '₹',
        fractionalUnit: {
          name: 'Paisa',
          plural: 'Paise',
          symbol: '',
        },
      },
    },
  });

  const [rawHTML, setRawHTML] = useState('');
  const [showHTML, setShowHTML] = useState(false);
  const [companyData, setCompanyData] = useState<any>(null);

  const data = JSON.parse(localStorage.getItem('proforma') as string);
  const proformaData = data.proforma;
  const proformaItems = JSON.parse(localStorage.getItem('proformaItems') as string);
  const { data: companies } = useReadCompanies();

  const printRef = useRef<HTMLDivElement>(null);

  const handlePrint = useReactToPrint({
    content: () => printRef.current,
  });

  useEffect(() => {
    if (companies && proformaData && proformaItems) {
      const company = companies.find(
        (c: any) => c.name === proformaData.company || c.id.toString() === proformaData.company
      );
      setCompanyData(company);

      let rawHTMLString = `
        <table style="width: 100%; border-collapse: collapse; font-family: Arial, sans-serif;">
          <tr>
            <td colspan="8" style="text-align: center; padding: 20px; border-bottom: 2px solid #000;">
              <h1 style="margin: 0; font-size: 24px;">NUNES TRAVELS</h1>
              <p style="margin: 5px 0;">Travel & Tourism Services</p>
              <p style="margin: 5px 0;">Phone: +91-832-2xxx-xxx | Email: <EMAIL></p>
            </td>
          </tr>
          <tr>
            <td colspan="8" style="text-align: center; padding: 15px; background-color: #f5f5f5;">
              <h2 style="margin: 0; font-size: 20px;">PROFORMA INVOICE</h2>
            </td>
          </tr>
          <tr>
            <td colspan="4" style="padding: 10px; border: 1px solid #ccc;">
              <strong>To:</strong><br/>
              ${company?.name || proformaData.company}<br/>
              ${company?.address || ''}<br/>
              ${company?.phone || ''}
            </td>
            <td colspan="4" style="padding: 10px; border: 1px solid #ccc;">
              <strong>Proforma No:</strong> ${proformaData.proformanum}<br/>
              <strong>Date:</strong> ${formatDate(proformaData.date)}<br/>
            </td>
          </tr>
          <tr style="background-color: #f0f0f0;">
            <th style="border: 1px solid #ccc; padding: 8px; text-align: left;">Sr. No.</th>
            <th style="border: 1px solid #ccc; padding: 8px; text-align: left;" colspan="3">Particulars</th>
            <th style="border: 1px solid #ccc; padding: 8px; text-align: center;">Qty</th>
            <th style="border: 1px solid #ccc; padding: 8px; text-align: right;">Rate</th>
            <th style="border: 1px solid #ccc; padding: 8px; text-align: right;" colspan="2">Amount</th>
          </tr>`;

      let total = 0;
      proformaItems.forEach((item: any, index: number) => {
        const amount = Number(item.amount) || 0;
        total += amount;
        
        rawHTMLString += `
          <tr>
            <td style="border: 1px solid #ccc; padding: 8px; text-align: center;">${index + 1}</td>
            <td style="border: 1px solid #ccc; padding: 8px;" colspan="3">${item.title || ''}</td>
            <td style="border: 1px solid #ccc; padding: 8px; text-align: center;">${item.quantity || 1}</td>
            <td style="border: 1px solid #ccc; padding: 8px; text-align: right;">${Number(item.rate || 0).toFixed(2)}</td>
            <td style="border: 1px solid #ccc; padding: 8px; text-align: right;" colspan="2">${amount.toFixed(2)}</td>
          </tr>`;
      });

      const discount = 0;
      const gstValue = 18; // Default GST
      const netTaxable = total - discount;
      const gstAmount = netTaxable * (gstValue / 100);
      const grandTotal = netTaxable + gstAmount;

      rawHTMLString += `
        <tr>
          <td colspan="5"></td>
          <td colspan="2"><strong>Total (Rs.)</strong></td>
          <td style="text-align: right">${total.toFixed(2)}</td>
        </tr>
        <tr>
          <td colspan="5"></td>
          <td colspan="2"><strong>Less (Rs.)</strong></td>
          <td style="text-align: right">${discount.toFixed(2)}</td>
        </tr>
        <tr>
          <td colspan="5"></td>
          <td colspan="2"><strong>GST ${gstValue}% (Rs.)</strong></td>
          <td style="text-align: right">${gstAmount.toFixed(2)}</td>
        </tr>
        <tr>
          <td colspan="5"><strong>${toWords.convert(grandTotal)}</strong></td>
          <td colspan="2"><strong>G. Total (Rs.)</strong></td>
          <td style="text-align: right"><strong>${grandTotal.toFixed(2)}</strong></td>
        </tr>
        </table>
        <div style="margin-top: 40px; text-align: right;">
          <p><strong>For NUNES TRAVELS</strong></p>
          <br/><br/>
          <p>Authorized Signatory</p>
        </div>`;

      setRawHTML(rawHTMLString);
      setShowHTML(true);
    }
  }, [companies, proformaData, proformaItems, toWords]);

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle
          text="Proforma Preview"
          primaryAction={
            <Button variant="contained" color="primary" onClick={handlePrint}>
              Print / Download
            </Button>
          }
        />
      </Grid>
      <Grid ref={printRef} item xs container spacing={2}>
        <Paper
          className="print-friendly"
          elevation={2}
          sx={{ width: '100%', margin: '1rem 3rem', padding: '0rem 0 9rem 0' }}
        >
          <Grid item xs={12} mx={4} my={4}>
            {showHTML && parse(rawHTML)}
          </Grid>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default ProformaView;

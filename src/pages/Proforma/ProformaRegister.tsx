import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';
import { useNavigate } from 'react-router-dom';

import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { Grid, Paper } from '@mui/material';

import MaterialTable, { Column } from '@material-table/core';

import DeleteDialog from '@components/Dialog/DeleteDialog';
import PageTitle from '@components/PageTitle';

import { formatDate } from '../../helpers/dateFormat';
import {
  useDeleteProformaReg,
  useReadProformaRegs,
} from '../../hooks/proforma';
import { IProforma } from '../../types/proforma';

const ProformaRegister = () => {
  const { enqueueSnackbar } = useSnackbar();

  const { data: proformas, isLoading, refetch } = useReadProformaRegs();

  const [localProformas, setLocalProformas] = useState<IProforma[]>([]);
  const [proforma, setProforma] = useState<IProforma | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const { mutate: deleteProformaRegMutate } = useDeleteProformaReg();

  const navigate = useNavigate();

  useEffect(() => {
    if (proformas) {
      // Filter only completed proformas (status = 1 means completed)
      const completedProformas = proformas.filter((p: any) => p.status === 1);
      const proformasWithSr = completedProformas.map((proformaItem, index) => ({
        ...proformaItem,
        sr: index + 1,
      }));
      setLocalProformas(proformasWithSr);
    }
  }, [proformas]);

  const handleDetails = (rowData: IProforma | undefined) => {
    if (rowData) {
      navigate(`/proformaDetails/${rowData.id}`);
    }
  };

  const handleEdit = (rowData: IProforma | IProforma[]) => {
    const selectedProforma = Array.isArray(rowData) ? rowData[0] : rowData;
    if (selectedProforma?.id) {
      navigate(`/proforma/${selectedProforma.id}/edit`);
    }
  };

  const handleDelete = (rowData: IProforma | IProforma[]) => {
    const selectedProforma = Array.isArray(rowData) ? rowData[0] : rowData;
    setProforma(selectedProforma);
    setDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  const handleConfirmDelete = () => {
    if (proforma?.id) {
      deleteProformaRegMutate(
        { id: proforma.id },
        {
          onSuccess: () => {
            enqueueSnackbar('Proforma deleted successfully', {
              variant: 'success',
            });
            refetch();
            setDeleteDialogOpen(false);
          },
          onError: () => {
            enqueueSnackbar('Failed to delete proforma', {
              variant: 'error',
            });
          },
        }
      );
    }
  };

  const columns: Column<IProforma>[] = [
    {
      title: 'Sr No',
      field: 'sr',
      render: rowData => `${rowData?.sr}`,
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '80px',
      },
    },
    {
      title: 'Date',
      field: 'date',
      render: rowData => formatDate(rowData?.date),
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '120px',
      },
    },
    {
      title: 'Proforma Number',
      field: 'proformanum',
      render: rowData => `${rowData?.proformanum || 'N/A'}`,
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '150px',
      },
    },
    {
      title: 'Company',
      field: 'company',
      render: rowData => `${rowData?.company || 'N/A'}`,
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '150px',
      },
    },
    {
      title: 'Total',
      field: 'total',
      render: rowData => `₹${rowData?.total || '0'}`,
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '100px',
      },
    },
  ];

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle text="Proforma Register" />
      </Grid>

      <Grid item xs container spacing={2}>
        <Paper elevation={2} sx={{ width: '100%' }}>
          <Grid item xs={12}>
            <MaterialTable
              title=""
              columns={columns}
              data={localProformas || []}
              isLoading={isLoading}
              options={{
                draggable: false,
                paging: true,
                pageSize: 20,
                pageSizeOptions: [10, 20, 30, 50],
                search: true,
                tableLayout: 'fixed',
                searchFieldVariant: 'outlined',
                actionsColumnIndex: -1,
              }}
              style={{
                minHeight: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
              components={{
                Container: props => <Paper elevation={0} {...props} />,
              }}
              onRowClick={(_, rowData) => handleDetails(rowData)}
              actions={[
                {
                  icon: EditIcon,
                  tooltip: 'Edit Proforma',
                  onClick: (_, rowData) => handleEdit(rowData),
                },
                {
                  icon: DeleteIcon,
                  tooltip: 'Delete Proforma',
                  onClick: (_, rowData) => handleDelete(rowData),
                },
              ]}
            />
          </Grid>
        </Paper>
      </Grid>

      <DeleteDialog
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        handleConfirmDelete={handleConfirmDelete}
      />
    </Grid>
  );
};

export default ProformaRegister;

import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';
import { useNavigate } from 'react-router-dom';

import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { Button, Grid, Paper } from '@mui/material';

import MaterialTable, { Column } from '@material-table/core';

import NewProforma from '@components/Proforma/NewProforma';
import AddDialog from '@components/Dialog/AddDialog';
import DeleteDialog from '@components/Dialog/DeleteDialog';
import PageTitle from '@components/PageTitle';

import { formatDate } from '../../helpers/dateFormat';
import { 
  useReadProformaCreates, 
  useDeleteProformaCreate, 
  useAddProformaCreate 
} from '../../hooks/proforma';
import { IProformaCreate } from '../../types/proforma';

const CreateProforma = () => {
  const { enqueueSnackbar } = useSnackbar();

  const { data: proformas, isLoading, refetch } = useReadProformaCreates();

  const [localProformas, setLocalProformas] = useState<IProformaCreate[]>([]);
  const [proforma, setProforma] = useState<IProformaCreate | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addDialogOpen, setAddDialogOpen] = useState(false);

  const { mutate: deleteProformaMutate } = useDeleteProformaCreate();
  const { mutate: addProformaMutate } = useAddProformaCreate();

  const navigate = useNavigate();

  useEffect(() => {
    if (proformas) {
      const proformasWithSr = proformas.map((proformaItem, index) => ({
        ...proformaItem,
        sr: index + 1,
      }));
      setLocalProformas(proformasWithSr);
    }
  }, [proformas]);

  const handleDetails = (rowData: IProformaCreate | undefined) => {
    if (rowData) {
      navigate(`/proformaDetails/${rowData.id}`);
    }
  };

  const handleEdit = (rowData: IProformaCreate | IProformaCreate[]) => {
    const selectedProforma = Array.isArray(rowData) ? rowData[0] : rowData;
    if (selectedProforma?.id) {
      navigate(`/proforma/${selectedProforma.id}/edit`);
    }
  };

  const handleDelete = (rowData: IProformaCreate | IProformaCreate[]) => {
    const selectedProforma = Array.isArray(rowData) ? rowData[0] : rowData;
    setProforma(selectedProforma);
    setDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  const handleConfirmDelete = () => {
    if (proforma?.id) {
      deleteProformaMutate(
        { id: proforma.id },
        {
          onSuccess: () => {
            enqueueSnackbar('Proforma deleted successfully', {
              variant: 'success',
            });
            refetch();
            setDeleteDialogOpen(false);
          },
          onError: () => {
            enqueueSnackbar('Failed to delete proforma', {
              variant: 'error',
            });
          },
        }
      );
    }
  };

  const handleAddDialogOpen = () => {
    setAddDialogOpen(true);
  };

  const handleAddDialogClose = () => {
    setAddDialogOpen(false);
  };

  const handleAddProforma = (data: any) => {
    addProformaMutate(
      {
        date: data.date,
        company: data.company,
        status: 'pending',
      },
      {
        onSuccess: () => {
          enqueueSnackbar('Proforma created successfully', {
            variant: 'success',
          });
          refetch();
          setAddDialogOpen(false);
        },
        onError: () => {
          enqueueSnackbar('Failed to create proforma', {
            variant: 'error',
          });
        },
      }
    );
  };

  const columns: Column<IProformaCreate>[] = [
    {
      title: 'Sr No',
      field: 'sr',
      render: rowData => `${rowData?.sr}`,
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '80px',
      },
    },
    {
      title: 'Date',
      field: 'date',
      render: rowData => formatDate(rowData?.date),
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '120px',
      },
    },
    {
      title: 'Company',
      field: 'company',
      render: rowData => `${rowData?.company}`,
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '150px',
      },
    },
    {
      title: 'Status',
      field: 'status',
      render: rowData => `${rowData?.status}`,
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '100px',
      },
    },
  ];

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle
          text="Create Proforma"
          primaryAction={
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddDialogOpen}
            >
              <AddIcon sx={{ mr: 1 }} />
              Add Proforma
            </Button>
          }
        />
      </Grid>

      <Grid item xs container spacing={2}>
        <Paper elevation={2} sx={{ width: '100%' }}>
          <Grid item xs={12}>
            <MaterialTable
              title=""
              columns={columns}
              data={localProformas || []}
              isLoading={isLoading}
              options={{
                draggable: false,
                paging: false,
                search: true,
                tableLayout: 'fixed',
                searchFieldVariant: 'outlined',
                actionsColumnIndex: -1,
              }}
              style={{
                minHeight: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
              components={{
                Container: props => <Paper elevation={0} {...props} />,
              }}
              onRowClick={(_, rowData) => handleDetails(rowData)}
              actions={[
                {
                  icon: EditIcon,
                  tooltip: 'Edit Proforma',
                  onClick: (_, rowData) => handleEdit(rowData),
                },
                {
                  icon: DeleteIcon,
                  tooltip: 'Delete Proforma',
                  onClick: (_, rowData) => handleDelete(rowData),
                },
              ]}
            />
          </Grid>
        </Paper>
      </Grid>

      <AddDialog
        open={addDialogOpen}
        handleClose={handleAddDialogClose}
        ContentComponent={NewProforma}
        onSubmit={handleAddProforma}
      />

      <DeleteDialog
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        handleConfirmDelete={handleConfirmDelete}
      />
    </Grid>
  );
};

export default CreateProforma;

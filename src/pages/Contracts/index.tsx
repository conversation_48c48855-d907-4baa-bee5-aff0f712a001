import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';
import { useNavigate } from 'react-router-dom';

import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { Button, Grid, Paper } from '@mui/material';

import MaterialTable, { Column } from '@material-table/core';

import NewContracts from '@components/Contracts/NewContracts';
import AddDialog from '@components/Dialog/AddDialog';
import DeleteDialog from '@components/Dialog/DeleteDialog';
import PageTitle from '@components/PageTitle';

import { formatDate } from '../../helpers/dateFormat';
import {
  useAddContract,
  useDeleteContract,
  useReadContracts,
} from '../../hooks/contract';
import { IContract } from '../../types/contract';

const Contract = () => {
  const { enqueueSnackbar } = useSnackbar();

  const { data: contracts, isLoading, refetch } = useReadContracts();

  const [localContracts, setLocalContracts] = useState<IContract[]>([]);
  const [contract, setContract] = useState<IContract | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addDialogOpen, setAddDialogOpen] = useState(false);

  const { mutate: deleteContractMutate } = useDeleteContract();
  const { mutate: addContractMutate } = useAddContract();

  const navigate = useNavigate();

  useEffect(() => {
    if (contracts) {
      const contractsWithSr = contracts.map((contractItems, index) => ({
        ...contractItems,
        sr: index + 1,
      }));
      setLocalContracts(contractsWithSr);
    }
  }, [contracts]);

  const handleDetails = (rowData: IContract | undefined) => {
    if (rowData) {
      navigate(`/contractDetails/${rowData.id}`);
    }
  };

  const handleEdit = (rowData: IContract | IContract[]) => {
    const selectedContract = Array.isArray(rowData) ? rowData[0] : rowData;
    if (selectedContract?.id) {
      navigate(`/contracts/${selectedContract.id}/edit`);
    }
  };

  const handleDelete = (rowData: IContract | IContract[]) => {
    const selectedContract = Array.isArray(rowData) ? rowData[0] : rowData;
    setContract(selectedContract);
    setDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  const handleConfirmDelete = () => {
    if (contract) {
      deleteContractMutate(
        { id: +contract.id },
        {
          onSuccess: () => {
            setLocalContracts(prevContracts =>
              prevContracts.filter(c => c.id !== contract.id)
            );
            enqueueSnackbar('Content Deleted', { variant: 'success' });
          },
          onError: () => {
            enqueueSnackbar('Failed to Delete Content', {
              variant: 'error',
            });
          },
        }
      );
    }
    setDeleteDialogOpen(false);
  };

  const handleAddDialogOpen = () => {
    setAddDialogOpen(true);
  };

  const handleAddDialogClose = (action: any) => {
    if (action && action?.action) {
      addContractMutate(action?.formData, {
        onSuccess: () => {
          refetch();
          setAddDialogOpen(false);
        },
        onError: () => {
          enqueueSnackbar('Failed to Add Content', { variant: 'error' });
        },
      });
    } else {
      setAddDialogOpen(false);
    }
  };

  const columns: Column<IContract>[] = [
    {
      title: 'S.No',
      field: 'sr',
      sorting: true,
      width: '50px',
      headerStyle: {
        textAlign: 'center',
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        paddingLeft: '12px',
      },
      cellStyle: {
        textAlign: 'center',
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Date',
      field: 'date',
      render: rowData => `${formatDate(rowData?.date)}`,
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Bill No.',
      field: 'billno',
      render: rowData => `${rowData?.billno}`,
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Company',
      field: 'company',
      render: rowData => `${rowData?.company}`,
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '150px',
      },
    },
  ];

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle
          text="Contracts"
          primaryAction={
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddDialogOpen}
            >
              <AddIcon sx={{ mr: 1 }} />
              Add Contract
            </Button>
          }
        />
      </Grid>

      <Grid item xs container spacing={2}>
        <Paper elevation={2} sx={{ width: '100%' }}>
          <Grid item xs={12}>
            <MaterialTable
              title=""
              columns={columns}
              data={localContracts || []}
              isLoading={isLoading}
              options={{
                draggable: false,
                paging: false,
                search: true,
                tableLayout: 'fixed',
                searchFieldVariant: 'outlined',
                actionsColumnIndex: -1,
              }}
              style={{
                minHeight: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
              components={{
                Container: props => <Paper elevation={0} {...props} />,
              }}
              onRowClick={(_, rowData) => handleDetails(rowData)}
              actions={[
                {
                  icon: EditIcon,
                  tooltip: 'Edit Contract',
                  onClick: (_, rowData) => handleEdit(rowData),
                },
                {
                  icon: DeleteIcon,
                  tooltip: 'Delete Contract',
                  onClick: (_, rowData) => handleDelete(rowData),
                },
              ]}
            />
          </Grid>
        </Paper>
      </Grid>

      <AddDialog
        open={addDialogOpen}
        handleClose={handleAddDialogClose}
        ContentComponent={NewContracts}
      />

      {/* <DetailDialog
        open={drawerOpen}
        handleCloseDrawer={() => setDrawerOpen(false)}
        ContentComponent={ContractDetails}
        data={contract}
      /> */}

      {/* <EditDialog
        open={editDialogOpen}
        handleClose={handleCloseEditDialog}
        ContentComponent={EditContracts}
        billMade={false}
        defaultvalue={defaultvalue}
      /> */}

      <DeleteDialog
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        handleConfirmDelete={handleConfirmDelete}
      />
    </Grid>
  );
};

export default Contract;

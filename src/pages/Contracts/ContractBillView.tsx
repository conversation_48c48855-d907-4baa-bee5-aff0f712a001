import { useEffect, useMemo, useRef, useState } from 'react';

import parse from 'html-react-parser';
import { useSnackbar } from 'notistack';
import { CSVLink } from 'react-csv';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useReactToPrint } from 'react-to-print';
import { ToWords } from 'to-words';

import { Button, Grid, Paper } from '@mui/material';

import PageTitle from '@components/PageTitle';

import { formatDate } from '../../helpers/dateFormat';
import { getFinancialYear } from '../../helpers/financialYear';
import { useAddBillReg, useGetBillRegByLastCreated } from '../../hooks/bill';
import { useReadCompanies } from '../../hooks/company';
import {
  useEditContract,
  useReadContract,
  useReadCpart,
} from '../../hooks/contract';

const ContractBillView = () => {
  const toWords = useMemo(
    () =>
      new ToWords({
        localeCode: 'en-IN',
        converterOptions: {
          currency: true,
          ignoreDecimal: false,
          ignoreZeroCurrency: false,
          doNotAddOnly: false,
          currencyOptions: {
            name: 'Rupee',
            plural: 'Rupees',
            symbol: '₹',
            fractionalUnit: {
              name: 'Paisa',
              plural: 'Paise',
              symbol: '',
            },
          },
        },
      }),
    []
  );

  const { enqueueSnackbar } = useSnackbar();
  const { id } = useParams();
  const [rawHTML, setRawHTML] = useState('');
  const [billNo, setBillNo] = useState('');
  const [grandTotalState, setGrandTotalState] = useState(0);
  const [showHTML, setShowHTML] = useState(false);
  const [showCsvData, setShowCsvData] = useState(false);
  const [csvData, setCsvData] = useState<any[]>([]);
  const [companyData, setCompanyData] = useState<any>(null);
  const [contractData, setContractData] = useState<any>(null);
  const [contractItems, setContractItems] = useState<any[]>([]);

  const { data: contract, isLoading: contractLoading } = useReadContract({
    id: id || '',
  });
  const { data: cpartData, isLoading: cpartLoading } = useReadCpart(id || '');
  const { data: companies } = useReadCompanies();
  const { data: lastCreatedBillReg } = useGetBillRegByLastCreated();
  const { mutate: addBillRegMutate } = useAddBillReg();
  const { mutate: editContractMutate } = useEditContract();
  const { getBillPrefix } = getFinancialYear();

  const navigate = useNavigate();
  const location = useLocation();
  const printRef = useRef<HTMLDivElement>(null);

  const print = useReactToPrint({
    content: () => printRef.current,
  });

  // Load contract and company data
  useEffect(() => {
    if (contract && companies) {
      setContractData(contract);

      const company = companies.find(
        (c: any) =>
          c.id === contract.company || c.id.toString() === contract.company
      );

      if (company) {
        setCompanyData(company);
      }
    }
  }, [contract, companies]);

  // Load contract items
  useEffect(() => {
    if (cpartData) {
      const mappedItems = cpartData.map((item: any) => ({
        ...item,
        vehicle: item.vtype || '', // Map vtype to vehicle
        title: item.particular || '', // Map particular to title
        quantity: item.kms || 0, // Map kms to quantity
        rate: item.rate || 0,
        amount: item.amount || 0,
      }));

      console.log('ContractBillView - Raw cpartData:', cpartData);
      console.log('ContractBillView - Mapped items:', mappedItems);

      setContractItems(mappedItems);
    }
  }, [cpartData]);

  // Generate bill number
  const currentBillNumber = useMemo(() => {
    if (lastCreatedBillReg) {
      const billCount = Number(lastCreatedBillReg.id) + 1;
      return `${getBillPrefix()}/${billCount}`;
    }
    return `${getBillPrefix()}/101`;
  }, [lastCreatedBillReg, getBillPrefix]);

  useEffect(() => {
    if (currentBillNumber !== billNo) {
      setBillNo(currentBillNumber);
    }
  }, [currentBillNumber, billNo]);

  // Generate the bill HTML and CSV data
  useEffect(() => {
    const generateBillHTML = () => {
      if (!contractData || !companyData || !contractItems.length) return;

      let rawHTMLString = ``;
      const today = new Date();

      // Calculate totals
      let total = 0;
      contractItems.forEach((item: any) => {
        total += Number(item.amount || 0);
      });

      const discount = Number(contractData.discount || 0);
      const netTaxable = total - discount;
      const gstValue = Number(companyData?.gst) || 0;
      const gstAmount = netTaxable * (gstValue / 100);
      const grandTotal = netTaxable + gstAmount;
      setGrandTotalState(grandTotal);

      // Header section
      rawHTMLString += `
      <div style="padding-left:20px; padding-right:40px">
        <div style="padding-top:140px; width: 50%; font-size: 10px;">
          <p style="margin: 0;"><strong>Date: </strong>${formatDate(today.toISOString())}</p>
          <p style="margin: 0;"><strong>To: </strong>${companyData?.name || ''}</p>
          <p style="margin: 0;"><strong>Address: </strong>${companyData?.address || ''}</p>
          <p style="margin: 0;"><strong>GST Number: </strong>${companyData?.gstno || ''}</p>
          <p style="margin: 0;"><strong>PAN Number: </strong>${companyData?.panno || ''}</p>
          <p style="margin: 0;"><strong>Bill No: </strong>${billNo || ''}</p>
        </div>
      `;

      // GST service description
      if (companyData.gst === '0' && companyData.gsttype === '3') {
        rawHTMLString += `<p style="padding-top:5px;padding-bottom:5px;text-align:left;font-size: 10px;"><strong>Transport of passengers under contract carriage other than motor cab, exempt services provided to educational institution SAC - 996419 / 996413.</strong></p>`;
      } else if (companyData.gst === '5') {
        rawHTMLString += `<p style="padding-top:5px;padding-bottom:5px;text-align:left;font-size: 10px;"><strong>Hire / Rental services of passenger vehicles, with or without operators SAC – 996601. (Under Sr. No. 10(i) of Noti. 11/2017 & 31/2017)</strong></p>`;
      } else if (companyData.gst === '18' || companyData.gst === '12') {
        rawHTMLString += `<p style="padding-top:5px;padding-bottom:5px;text-align:left;font-size: 10px;"><strong>Hire / Rental services of passenger vehicles, with or without operators SAC – 996601. (Under Sr. No. 10(ii) of Noti. 11/2017)</strong></p>`;
      }

      // Table header
      rawHTMLString += `
        <table id="table-h" border="1" cellspacing="0" cellpadding="3" width="100%" style="font-size:8px;">
          <tr valign="top">
            <td style="border: 1px solid black;" width="80px" align="center"><strong>Vehicle</strong></td>
            <td style="border: 1px solid black;" width="200px" align="center"><strong>Particulars</strong></td>
            <td style="border: 1px solid black;" width="100px" align="center"><strong>KMs/No. of Nights</strong></td>
            <td style="border: 1px solid black;" width="80px" align="center"><strong>Rate</strong></td>
            <td style="border: 1px solid black;" width="100px" align="right"><strong>Amount (₹)</strong></td>
          </tr>
      `;

      // Add contract items
      contractItems.forEach((item: any) => {
        rawHTMLString += `
          <tr valign="top">
            <td style="border: 1px solid black;" align="center">${item.vehicle || ''}</td>
            <td style="border: 1px solid black;" align="left">${item.title || ''}</td>
            <td style="border: 1px solid black;" align="center">${item.quantity || 0}</td>
            <td style="border: 1px solid black;" align="right">${Number(item.rate || 0).toFixed(2)}</td>
            <td style="border: 1px solid black;" align="right">${Number(item.amount || 0).toFixed(2)}</td>
          </tr>
        `;
      });

      // Add totals
      rawHTMLString += `
          <tr><td colspan="5">&nbsp;</td></tr>
          <tr>
            <td colspan="4"><strong>Total (Rs.)</strong></td>
            <td align="right"><strong>${total.toFixed(2)}</strong></td>
          </tr>
      `;

      if (discount > 0) {
        rawHTMLString += `
          <tr>
            <td colspan="4"><strong>Less (Rs.)</strong></td>
            <td align="right"><strong>${discount.toFixed(2)}</strong></td>
          </tr>
        `;
      }

      if (gstValue > 0) {
        const cgst = gstAmount / 2;
        const sgst = gstAmount / 2;
        rawHTMLString += `
          <tr>
            <td colspan="4"><strong>CGST @ ${(gstValue / 2).toFixed(1)}%</strong></td>
            <td align="right"><strong>${cgst.toFixed(2)}</strong></td>
          </tr>
          <tr>
            <td colspan="4"><strong>SGST @ ${(gstValue / 2).toFixed(1)}%</strong></td>
            <td align="right"><strong>${sgst.toFixed(2)}</strong></td>
          </tr>
        `;
      }

      rawHTMLString += `
          <tr>
            <td colspan="4"><strong>${toWords.convert(grandTotal)}</strong></td>
            <td align="right"><strong>G. Total (Rs.) ${grandTotal.toFixed(2)}</strong></td>
          </tr>
        </table>
      `;

      if (companyData.gst === '0' && companyData.gsttype !== '3') {
        rawHTMLString += `
          <p style="padding-top:15px;padding-bottom:5px;text-align:left;font-size:10px;"><strong>5% Goods &amp; Services Tax payable by Service Recipient under Reverse Charge Mechanism ${(netTaxable * 0.05).toFixed(2)}/-</strong></p>
        `;
      }

      rawHTMLString += `</div>`;
      setRawHTML(rawHTMLString);
      setShowHTML(true);
    };

    const generateCSVData = () => {
      if (!contractData || !companyData || !contractItems.length) return;

      const csvDataToAdd = [
        [],
        ['Date', formatDate(new Date().toISOString())],
        ['To', companyData?.name || ''],
        ['Bill No', billNo],
        [],
        ['Vehicle', 'Particulars', 'KMs/No. of Nights', 'Rate', 'Amount'],
      ];

      contractItems.forEach((item: any) => {
        csvDataToAdd.push([
          item.vehicle || '',
          item.title || '',
          item.quantity || 0,
          item.rate || 0,
          item.amount || 0,
        ]);
      });

      setCsvData(csvDataToAdd);
      setShowCsvData(true);
    };

    if (contractData && companyData && contractItems.length > 0) {
      generateBillHTML();
      generateCSVData();
    }
  }, [contractData, companyData, contractItems, billNo, toWords]);

  const handlePrint = () => {
    let billCount = 101;
    if (lastCreatedBillReg) {
      billCount = Number(lastCreatedBillReg.id) + 1;
    }

    if (
      !contractData.billno ||
      contractData.billno === '0' ||
      contractData.billno === ''
    ) {
      // Create new bill register entry
      addBillRegMutate(
        {
          id: billCount,
          date: new Date(),
          billnum: billNo,
          type: '2',
          total: grandTotalState,
          company: companyData?.name,
        },
        {
          onSuccess: () => {
            // Update contract with bill number
            editContractMutate(
              { billno: billCount, id },
              {
                onSuccess: () => {
                  setBillNo(billNo);
                  print();

                  setTimeout(() => {
                    const from = location.state?.from;
                    if (from === '/contracts') {
                      navigate('/contracts');
                    } else {
                      navigate(-1);
                    }
                  }, 2000);
                },
                onError: () => {
                  enqueueSnackbar('Failed to Update Contract', {
                    variant: 'error',
                  });
                },
              }
            );
          },
          onError: () => {
            enqueueSnackbar('Failed to Create Bill', { variant: 'error' });
          },
        }
      );
    } else {
      setBillNo(billNo);
      print();
    }
  };

  const handleExport = (_: any, done: any) => {
    done(true);
    setTimeout(() => {
      const from = location.state?.from;
      if (from === '/contracts') {
        navigate('/contracts');
      } else {
        navigate(-1);
      }
    }, 2000);
  };

  if (contractLoading || cpartLoading) {
    return <div>Loading...</div>;
  }

  return (
    <Grid container direction="column" spacing={3} sx={{ p: 3 }}>
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle text="Contract Bill" />
      </Grid>
      <Grid item xs={2} sm={1} alignSelf="flex-end" py={2}>
        <Button
          style={{ paddingLeft: '58px', paddingRight: '58px' }}
          variant="contained"
          color="primary"
          onClick={handlePrint}
        >
          Print
        </Button>
      </Grid>
      <Grid item xs={2} sm={1} alignSelf="flex-end" py={2}>
        {showCsvData && (
          <CSVLink
            filename={`contract_bill_${companyData?.name}.csv`}
            data={csvData}
            asyncOnClick
            onClick={(event, done) => handleExport(event, done)}
          >
            <Button variant="contained" color="primary">
              Download CSV
            </Button>
          </CSVLink>
        )}
      </Grid>
      <Grid ref={printRef} item xs container spacing={2}>
        <Paper
          className="print-friendly"
          elevation={2}
          sx={{
            width: '100%',
            margin: '1rem 3rem',
            padding: '0rem 0 9rem 0',
          }}
        >
          <Grid item xs={12} mx={4} my={4}>
            {showHTML && <div ref={printRef}>{parse(rawHTML)}</div>}
          </Grid>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default ContractBillView;

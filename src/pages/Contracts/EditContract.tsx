import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';
import { useNavigate, useParams } from 'react-router-dom';

import AddIcon from '@mui/icons-material/Add';
import CancelIcon from '@mui/icons-material/Cancel';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import {
  Box,
  Button,
  CircularProgress,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';

import PageTitle from '@components/PageTitle';

import apiClient from '@constants/axios';

// import { formatDate } from '../../helpers/dateFormat';
import { useReadCompanies } from '../../hooks/company';
import {
  useAddCPart,
  useDeleteCPart,
  useEditContract,
  useEditCPart,
  useReadContract,
  useReadCpart,
} from '../../hooks/contract';

interface ContractItem {
  id?: string;
  title: string;
  particular?: string; // Add particular field
  quantity: string;
  rate: string;
  amount: number;
  isEditing?: boolean;
  isNew?: boolean;
}

const EditContract = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  // Contract basic details
  const [contractDate, setContractDate] = useState('');
  const [contractCompany, setContractCompany] = useState('');

  // Contract items state
  const [contractItems, setContractItems] = useState<ContractItem[]>([]);
  const [editingItemId, setEditingItemId] = useState<string | null | undefined>(
    null
  );
  const [isSaving, setIsSaving] = useState(false);
  const [isGeneratingBill, setIsGeneratingBill] = useState(false);

  // Hooks - destructure with proper types
  const contractId = id as string;

  // Only fetch data if we have a valid ID
  const { data: contractData, isLoading: contractLoading } = useReadContract({
    id: contractId,
  });

  const { data: cpartData, isLoading: cpartLoading } = useReadCpart(contractId);
  const { data: companies } = useReadCompanies();
  const { mutate: editContractMutate } = useEditContract();
  const { mutate: addCPartMutate } = useAddCPart();
  const { mutate: editCPartMutate } = useEditCPart();
  const { mutate: deleteCPartMutate } = useDeleteCPart();

  // Loading state - only declare once at the top level
  const isLoading = contractLoading || cpartLoading || !contractId;

  // Calculate total amount, ensuring amount is a number
  const totalAmount = contractItems.reduce((sum, item) => {
    const amount =
      typeof item.amount === 'number' ? item.amount : Number(item.amount) || 0;
    return sum + amount;
  }, 0);

  // Load contract data - Enhanced with better debugging and validation
  useEffect(() => {
    if (contractData) {
      console.log('=== CONTRACT DATA LOADED ===');
      console.log('Full contract data:', contractData);
      console.log('Contract company field:', contractData.company);
      console.log('Contract company type:', typeof contractData.company);

      // Safely access contract data with proper type checking
      setContractDate(contractData.date || '');

      // Handle company ID - ensure it's a string for the dropdown
      const companyValue = contractData.company?.toString() || '';
      console.log('Converted company value:', companyValue);
      console.log('Setting contractCompany state to:', companyValue);
      setContractCompany(companyValue);
    } else {
      console.log('Contract data is null/undefined');
    }
  }, [contractData]);

  // Debug companies data with enhanced logging
  useEffect(() => {
    if (companies) {
      console.log('=== COMPANIES DATA LOADED ===');
      console.log('Companies array:', companies);
      console.log('Companies length:', companies.length);
      if (companies.length > 0) {
        console.log('First company structure:', companies[0]);
        console.log(
          'Company IDs available:',
          companies.map(c => ({ id: c.id, name: c.name }))
        );
      }
    } else {
      console.log('Companies data is null/undefined');
    }
  }, [companies]);

  // Combined effect to check when both data sets are available and handle company matching
  useEffect(() => {
    if (contractData && companies && companies.length > 0) {
      console.log('=== BOTH CONTRACT AND COMPANIES DATA AVAILABLE ===');
      const contractCompanyValue = contractData.company?.toString() || '';
      console.log('Contract company value:', contractCompanyValue);

      // Try to find company by ID first
      let matchingCompany = companies.find(
        c => c.id.toString() === contractCompanyValue
      );
      console.log('Matching company by ID:', matchingCompany);

      // If not found by ID, try to find by name (for legacy data)
      if (!matchingCompany && contractCompanyValue) {
        matchingCompany = companies.find(c => c.name === contractCompanyValue);
        console.log('Matching company by name:', matchingCompany);

        // If found by name, use the company ID for the dropdown
        if (matchingCompany) {
          const companyId = matchingCompany.id.toString();
          console.log('Found company by name, setting ID:', companyId);
          setContractCompany(companyId);
          return;
        }
      }

      // If found by ID or no match needed, set the value
      if (matchingCompany || contractCompanyValue) {
        const valueToSet = matchingCompany
          ? matchingCompany.id.toString()
          : contractCompanyValue;
        console.log('Setting company value to:', valueToSet);
        if (valueToSet !== contractCompany) {
          setContractCompany(valueToSet);
        }
      }
    }
  }, [contractData, companies, contractCompany]);

  // Load contract items
  useEffect(() => {
    if (cpartData) {
      console.log('=== LOADING CPART DATA ===');
      console.log('Raw cpartData:', cpartData);

      setContractItems(
        cpartData.map(
          (item: {
            id: number;
            vtype?: string;
            kms?: number;
            rate?: number;
            amount?: number;
            particular?: string;
          }) => {
            // Ensure amount is a number, default to 0 if undefined or invalid
            const amount =
              typeof item.amount === 'number'
                ? item.amount
                : Number(item.amount) || 0;

            console.log('Mapping item:', {
              id: item.id,
              vtype: item.vtype,
              particular: item.particular,
              kms: item.kms,
              rate: item.rate,
              amount: item.amount,
            });

            return {
              id: item.id.toString(),
              title: item.vtype || '', // Map vtype to title
              particular: item.particular || '', // Map particular field
              quantity: item.kms?.toString() || '0', // Map kms to quantity
              rate: item.rate?.toString() || '0',
              amount,
              isEditing: false,
            };
          }
        )
      );
    }
  }, [cpartData]);

  // Set editing item ID with proper type handling
  const setEditingItemIdSafe = (itemId: string | null) => {
    setEditingItemId(itemId);
  };

  // Handle adding a new item
  const handleAddItem = () => {
    const newItem: ContractItem = {
      id: `new-${Date.now()}`,
      title: '',
      particular: '', // Initialize particular field
      quantity: '0',
      rate: '0',
      amount: 0,
      isEditing: true,
      isNew: true,
    };
    setContractItems([...contractItems, newItem]);
    setEditingItemId(newItem.id);
  };

  // Handle editing an item
  const handleEditItem = (itemId: string) => {
    setEditingItemIdSafe(itemId);
    setContractItems(
      contractItems.map(item =>
        item.id === itemId ? { ...item, isEditing: true } : item
      )
    );
  };

  // Handle saving an item
  const handleSaveItem = (itemId: string): Promise<boolean> =>
    new Promise(resolve => {
      const itemToSave = contractItems.find(item => item.id === itemId);
      if (!itemToSave) {
        resolve(false);
        return;
      }

      // Validate required fields
      if (!itemToSave.title || !itemToSave.quantity || !itemToSave.rate) {
        enqueueSnackbar('Please fill in all fields', { variant: 'error' });
        resolve(false);
        return;
      }

      // Calculate amount
      const quantity = parseFloat(itemToSave.quantity) || 0;
      const rate = parseFloat(itemToSave.rate) || 0;
      const amount = quantity * rate;

      // Update the item
      const updatedItems = contractItems.map(item =>
        item.id === itemId
          ? {
              ...item,
              amount,
              isEditing: false,
              isNew: false,
            }
          : item
      );

      setContractItems(updatedItems);
      setEditingItemId(null);

      // Save to backend
      try {
        if (itemToSave.isNew) {
          console.log('Saving new item with data:', {
            cid: id,
            vtype: itemToSave.title,
            particular: itemToSave.particular,
            kms: quantity,
            rate,
            amount,
          });

          addCPartMutate(
            {
              cid: id as string, // Use cid instead of contract_id
              vtype: itemToSave.title, // Map title to vtype
              kms: quantity, // Map quantity to kms
              particular: itemToSave.particular || '', // Use actual particular field
              rate,
              amount,
            },
            {
              onSuccess: () => resolve(true),
              onError: error => {
                console.error('Error saving item:', error);
                enqueueSnackbar('Failed to save item. Please try again.', {
                  variant: 'error',
                });
                resolve(false);
              },
            }
          );
        } else {
          console.log('Updating existing item with data:', {
            id: parseInt(itemId, 10),
            vtype: itemToSave.title,
            particular: itemToSave.particular,
            kms: quantity,
            rate,
            amount,
          });

          editCPartMutate(
            {
              id: parseInt(itemId, 10),
              vtype: itemToSave.title, // Map title to vtype
              kms: quantity, // Map quantity to kms
              particular: itemToSave.particular || '', // Use actual particular field
              rate,
              amount,
            },
            {
              onSuccess: () => resolve(true),
              onError: error => {
                console.error('Error saving item:', error);
                enqueueSnackbar('Failed to save item. Please try again.', {
                  variant: 'error',
                });
                resolve(false);
              },
            }
          );
        }
      } catch (error) {
        console.error('Error saving item:', error);
        enqueueSnackbar('Failed to save item. Please try again.', {
          variant: 'error',
        });
        resolve(false);
      }
    });

  // Handle canceling edit
  const handleCancelEdit = (itemId: string) => {
    if (contractItems.find(item => item.id === itemId)?.isNew) {
      // Remove the new item if canceling creation
      setContractItems(contractItems.filter(item => item.id !== itemId));
    } else {
      // Reset to original values if editing existing item
      setContractItems(
        contractItems.map(item =>
          item.id === itemId ? { ...item, isEditing: false } : item
        )
      );
    }
    setEditingItemId(null);
  };

  // Handle removing an item
  const handleRemoveItem = (itemId: string) => {
    if (window.confirm('Are you sure you want to remove this item?')) {
      setContractItems(contractItems.filter(item => item.id !== itemId));
      if (!itemId.startsWith('new-')) {
        // Ensure we're passing the correct type expected by deleteCPartMutate
        deleteCPartMutate({ id: parseInt(itemId, 10) });
      }
    }
  };

  // Handle field changes
  const handleItemFieldChange = (
    itemId: string,
    field: keyof ContractItem,
    value: string
  ) => {
    console.log(`Field change: ID=${itemId}, field=${field}, value="${value}"`);

    const updatedItems = contractItems.map(item => {
      if (item.id === itemId) {
        const updatedItem = { ...item, [field]: value };

        // Recalculate amount if quantity or rate changes
        if (
          (field === 'quantity' || field === 'rate') &&
          editingItemId === itemId
        ) {
          const quantity =
            field === 'quantity'
              ? parseFloat(value) || 0
              : parseFloat(updatedItem.quantity) || 0;
          const rate =
            field === 'rate'
              ? parseFloat(value) || 0
              : parseFloat(updatedItem.rate) || 0;
          updatedItem.amount = quantity * rate;
          console.log(
            `Recalculated amount: ${updatedItem.amount} (quantity: ${quantity}, rate: ${rate})`
          );
        }

        console.log(`Updated item:`, updatedItem);
        return updatedItem;
      }
      return item;
    });

    console.log(`Setting contract items:`, updatedItems);
    setContractItems(updatedItems);
  };

  // Handle saving the entire contract

  const handleSaveContract = async (): Promise<boolean> => {
    if (!contractData) return false;

    setIsSaving(true);

    try {
      // Validate contract data
      if (!contractDate || !contractCompany) {
        enqueueSnackbar('Please fill in all required fields', {
          variant: 'error',
        });
        setIsSaving(false);
        return false;
      }

      console.log('Starting contract save process...');

      // Find the company name from the ID since the database expects company names
      const selectedCompany = companies?.find(
        (company: any) => company.id.toString() === contractCompany
      );
      const companyName = selectedCompany
        ? selectedCompany.name
        : contractCompany;

      console.log('Company mapping:', {
        contractCompany,
        selectedCompany,
        companyName,
      });

      const contractUpdateData = {
        id: contractData.id,
        date: contractDate,
        company: companyName, // Save company name as the database expects VARCHAR
      };

      console.log(
        'About to make direct API call with data:',
        contractUpdateData
      );

      // Make direct API call instead of using mutation
      const response = await apiClient.put(
        `/contract/${contractData.id}`,
        contractUpdateData
      );

      console.log('Direct API call response:', response);
      console.log('Response status:', response.status);
      console.log('Response data:', response.data);

      if (response.status >= 200 && response.status < 300) {
        console.log('Contract updated successfully');
        enqueueSnackbar('Contract saved successfully', { variant: 'success' });
        setIsSaving(false);
        return true;
      }

      console.error('Unexpected response status:', response.status);
      enqueueSnackbar('Failed to save contract. Please try again.', {
        variant: 'error',
      });
      setIsSaving(false);
      return false;
    } catch (error: any) {
      console.error('Error in handleSaveContract:', error);
      console.error('Error response:', error.response);
      console.error('Error status:', error.response?.status);
      console.error('Error data:', error.response?.data);

      enqueueSnackbar('Failed to save contract. Please try again.', {
        variant: 'error',
      });
      setIsSaving(false);
      return false;
    }
  };

  // Handle generating bill - ROBUST VERSION WITH SEQUENTIAL PROCESSING
  const handleGenerateBill = async () => {
    console.log('1. Starting handleGenerateBill');
    if (!contractData) {
      console.error('No contract data available');
      return;
    }

    setIsGeneratingBill(true);
    console.log('2. isGeneratingBill set to true');

    try {
      // 1. First save any currently editing item
      if (editingItemId) {
        console.log('3. Saving currently editing item:', editingItemId);
        const saveSuccess = await handleSaveItem(editingItemId);
        if (!saveSuccess) {
          console.error('4. Failed to save item');
          enqueueSnackbar(
            'Failed to save item. Please complete or cancel editing first.',
            { variant: 'error' }
          );
          return;
        }
        console.log('4. Item saved successfully');
      } else {
        console.log('3. No item currently being edited');
      }

      // 2. Validate contract data
      console.log('5. Validating contract data');
      if (!contractDate || !contractCompany) {
        console.error('6. Missing required contract data:', {
          contractDate,
          contractCompany,
        });
        enqueueSnackbar('Please fill in all required contract details', {
          variant: 'error',
        });
        return;
      }

      if (contractItems.length === 0) {
        console.error('7. No items in contract');
        enqueueSnackbar('Please add at least one item to generate bill', {
          variant: 'error',
        });
        return;
      }

      // 3. Save the contract
      console.log('8. Saving contract data');
      await new Promise<void>((resolve, reject) => {
        console.log('9. Starting contract save mutation');
        const timeoutId = setTimeout(() => {
          console.error('9.1 Contract save timeout after 10 seconds');
          reject(new Error('Contract save timeout'));
        }, 10000);

        editContractMutate(
          {
            id: contractData.id,
            date: contractDate,
            company: contractCompany,
          },
          {
            onSuccess: () => {
              clearTimeout(timeoutId);
              console.log('10. Contract saved successfully');
              resolve();
            },
            onError: error => {
              clearTimeout(timeoutId);
              console.error('10. Error saving contract:', error);
              reject(error);
            },
          }
        );
      });

      // 4. Save all contract items - SEQUENTIAL PROCESSING (NO Promise.all)
      console.log('11. Saving contract items sequentially');
      console.log('11.1 Total items to process:', contractItems.length);

      // Filter out items that are currently being edited
      const itemsToSave = contractItems.filter(item => !item.isEditing);
      console.log('11.2 Items to save after filtering:', itemsToSave.length);

      if (itemsToSave.length === 0) {
        console.log('11.3 No items to save, skipping item save step');
      } else {
        // Process all items in parallel using Promise.allSettled
        console.log('12. Processing all items in parallel');

        const itemPromises = itemsToSave.map((item, index) => {
          console.log(
            `12.${index} Processing item:`,
            item.id,
            'Title:',
            item.title
          );

          // Ensure numeric values are properly converted
          const quantity = parseFloat(item.quantity) || 0;
          const rate = parseFloat(item.rate) || 0;
          const amount = quantity * rate;

          console.log(`12.${index}.1 Item values:`, { quantity, rate, amount });

          const itemData = {
            cid: contractData.id, // Use cid instead of contract_id
            vtype: item.title, // Map title to vtype
            kms: item.quantity, // Map quantity to kms
            particular: item.particular || '', // Use actual particular field
            rate,
            amount,
          };

          // Return a promise for this item
          return new Promise<void>((resolve, reject) => {
            const timeoutId = setTimeout(() => {
              console.error(`13.${index}.1 Timeout for item:`, item.id);
              reject(new Error(`Timeout for item ${item.id}`));
            }, 15000);

            if (item.id && !item.id.startsWith('new-')) {
              // Editing existing item
              console.log(`13.${index} Updating existing item:`, item.id);
              editCPartMutate(
                {
                  ...itemData,
                  id: parseInt(item.id as string, 10),
                },
                {
                  onSuccess: () => {
                    clearTimeout(timeoutId);
                    console.log(
                      `14.${index} Item updated successfully:`,
                      item.id
                    );
                    resolve();
                  },
                  onError: error => {
                    clearTimeout(timeoutId);
                    console.error(
                      `14.${index} Error updating item:`,
                      item.id,
                      error
                    );
                    reject(error);
                  },
                }
              );
            } else {
              // Adding new item (both new- prefixed and fallback)
              console.log(`13.${index} Adding new item:`, item.id);
              addCPartMutate(itemData, {
                onSuccess: () => {
                  clearTimeout(timeoutId);
                  console.log(
                    `14.${index} New item added successfully:`,
                    item.id
                  );
                  resolve();
                },
                onError: error => {
                  clearTimeout(timeoutId);
                  console.error(
                    `14.${index} Error adding new item:`,
                    item.id,
                    error
                  );
                  reject(error);
                },
              });
            }
          });
        });

        // Wait for all items to complete (success or failure)
        const results = await Promise.allSettled(itemPromises);

        // Process results and show warnings for failed items
        let successCount = 0;
        let failureCount = 0;

        results.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            successCount += 1;
          } else {
            failureCount += 1;
            const item = itemsToSave[index];
            console.error(
              `Failed to process item ${index}:`,
              item.id,
              result.reason
            );
            enqueueSnackbar(`Failed to save item: ${item.title}`, {
              variant: 'warning',
            });
          }
        });

        console.log(
          `15. All items processed in parallel - Success: ${successCount}, Failed: ${failureCount}`
        );
      }

      // 5. Calculate all amounts for the bill
      console.log('16. Calculating bill amounts');
      const items = contractItems.map(item => ({
        ...item,
        quantity: parseFloat(item.quantity) || 0,
        rate: parseFloat(item.rate) || 0,
        amount: (parseFloat(item.quantity) || 0) * (parseFloat(item.rate) || 0),
      }));

      const subtotal = items.reduce((sum, item) => sum + item.amount, 0);
      console.log('17. Subtotal calculated:', subtotal);

      // 6. Prepare bill data
      console.log('18. Preparing bill data');
      const billData = {
        id: `B-${Date.now()}`,
        contractId: contractData.id,
        date: new Date().toISOString().split('T')[0],
        company: contractCompany,
        items: items.map(item => ({
          description: item.title,
          quantity: item.quantity,
          rate: item.rate,
          amount: item.amount,
        })),
        subtotal,
        tax: 0, // Add tax calculation if needed
        total: subtotal, // Update if tax is added
        status: 'unpaid',
      };

      // 7. Store the bill data in localStorage for the bill view
      console.log('19. Storing bill data in localStorage');
      try {
        localStorage.setItem('generatedBillData', JSON.stringify(billData));
        console.log('19.1 Data stored successfully');
      } catch (storageError) {
        console.error('19.1 Error storing data:', storageError);
        // Continue anyway, the navigation might still work
      }

      // 8. Show success message
      console.log('20. Showing success message');
      enqueueSnackbar('Bill generated successfully', { variant: 'success' });

      // 9. Navigate to the bill view with the contract ID
      console.log('21. Navigating to bill view');
      const navigationPath = `/contractBillView/${contractData.id}`;
      console.log('21.1 Navigation path:', navigationPath);

      // Add a small delay before navigation to ensure UI updates
      setTimeout(() => {
        try {
          console.log('22. Executing navigation');
          navigate(navigationPath);
          console.log('23. Navigation command executed');
        } catch (navError) {
          console.error('23. Navigation error:', navError);
          // Try alternative navigation
          console.log('23.1 Trying window.location.href fallback');
          window.location.href = navigationPath;
        }
      }, 100);
    } catch (error) {
      console.error('ERROR in handleGenerateBill:', error);
      enqueueSnackbar('Failed to generate bill. Please try again.', {
        variant: 'error',
      });
    } finally {
      // Delay the cleanup to ensure navigation completes
      setTimeout(() => {
        console.log('24. Final cleanup - setting isGeneratingBill to false');
        setIsGeneratingBill(false);
      }, 500);
    }
  };

  return (
    <Grid container direction="column" spacing={4} sx={{ p: 3 }}>
      <Grid item>
        <PageTitle text="Edit Contract" />
      </Grid>

      {isLoading ? (
        <Box display="flex" justifyContent="center" width="100%" p={4}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* Contract Details */}
          <Grid item>
            <Paper elevation={2} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Contract Details
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Date"
                    type="date"
                    value={contractDate}
                    onChange={e => setContractDate(e.target.value)}
                    InputLabelProps={{
                      shrink: true,
                    }}
                    inputProps={{
                      max: new Date().toISOString().split('T')[0], // Prevent future dates
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel id="company-select-label">Company</InputLabel>
                    <Select
                      labelId="company-select-label"
                      value={contractCompany || ''} // Ensure empty string if null/undefined
                      label="Company"
                      onChange={e => {
                        console.log(
                          'Company dropdown changed to:',
                          e.target.value
                        );
                        setContractCompany(e.target.value);
                      }}
                      displayEmpty
                    >
                      {!companies || companies.length === 0 ? (
                        <MenuItem value="" disabled>
                          Loading companies...
                        </MenuItem>
                      ) : (
                        companies.map((company: any) => {
                          const companyIdStr = company.id.toString();
                          const isSelected = companyIdStr === contractCompany;
                          // Only log for the selected company to reduce console spam
                          if (isSelected) {
                            console.log(
                              `✓ SELECTED COMPANY: ${company.name} (ID: ${companyIdStr})`
                            );
                          }
                          return (
                            <MenuItem key={company.id} value={companyIdStr}>
                              {company.name}
                            </MenuItem>
                          );
                        })
                      )}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Contract Items */}
          <Grid item>
            <Paper elevation={2} sx={{ p: 3 }}>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                mb={2}
              >
                <Typography variant="h6">Contract Items</Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={handleAddItem}
                  disabled={!!editingItemId || isGeneratingBill}
                >
                  Add Item
                </Button>
              </Box>

              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontSize: '0.9rem', fontWeight: 300 }}>
                        Vehicles
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.9rem', fontWeight: 300 }}>
                        Particulars
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.9rem', fontWeight: 300 }}>
                        KM/No of Nights
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.9rem', fontWeight: 300 }}>
                        Rate
                      </TableCell>
                      <TableCell sx={{ fontSize: '0.9rem', fontWeight: 300 }}>
                        Amount
                      </TableCell>
                      <TableCell
                        align="right"
                        sx={{ fontSize: '0.9rem', fontWeight: 300 }}
                      >
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {contractItems.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} align="center">
                          No items added yet. Click &apos;Add Item&apos; to get
                          started.
                        </TableCell>
                      </TableRow>
                    ) : (
                      contractItems.map(item => {
                        // Debug: Log current item state
                        if (item.isEditing) {
                          console.log(`Rendering editing item ${item.id}:`, {
                            title: item.title,
                            quantity: item.quantity,
                            rate: item.rate,
                            amount: item.amount,
                            isEditing: item.isEditing,
                          });
                        }

                        return (
                          <TableRow key={item.id}>
                            <TableCell>
                              {item.isEditing ? (
                                <TextField
                                  fullWidth
                                  size="small"
                                  value={item.title || ''}
                                  onChange={e => {
                                    console.log(
                                      `Title field onChange: "${e.target.value}"`
                                    );
                                    handleItemFieldChange(
                                      item.id!,
                                      'title',
                                      e.target.value
                                    );
                                  }}
                                  placeholder="Enter vehicle info"
                                  disabled={isGeneratingBill}
                                />
                              ) : (
                                item.title
                              )}
                            </TableCell>
                            <TableCell>
                              {item.isEditing ? (
                                <TextField
                                  fullWidth
                                  size="small"
                                  value={item.particular || ''}
                                  onChange={e => {
                                    console.log(
                                      `Particular field onChange: "${e.target.value}"`
                                    );
                                    handleItemFieldChange(
                                      item.id!,
                                      'particular',
                                      e.target.value
                                    );
                                  }}
                                  placeholder="Enter particulars"
                                  disabled={isGeneratingBill}
                                />
                              ) : (
                                item.particular || '-'
                              )}
                            </TableCell>
                            <TableCell>
                              {item.isEditing ? (
                                <TextField
                                  type="number"
                                  size="small"
                                  value={item.quantity}
                                  onChange={e =>
                                    handleItemFieldChange(
                                      item.id!,
                                      'quantity',
                                      e.target.value
                                    )
                                  }
                                  inputProps={{ min: 0, step: '0.01' }}
                                  sx={{ width: 120 }}
                                  disabled={isGeneratingBill}
                                />
                              ) : (
                                item.quantity
                              )}
                            </TableCell>
                            <TableCell>
                              {item.isEditing ? (
                                <TextField
                                  type="number"
                                  size="small"
                                  value={item.rate}
                                  onChange={e =>
                                    handleItemFieldChange(
                                      item.id!,
                                      'rate',
                                      e.target.value
                                    )
                                  }
                                  inputProps={{ min: 0, step: '0.01' }}
                                  sx={{ width: 120 }}
                                  disabled={isGeneratingBill}
                                />
                              ) : (
                                item.rate
                              )}
                            </TableCell>
                            <TableCell>
                              {item.isEditing ? (
                                <TextField
                                  type="number"
                                  size="small"
                                  value={item.amount}
                                  onChange={e =>
                                    setContractItems(
                                      contractItems.map(i =>
                                        i.id === item.id
                                          ? {
                                              ...i,
                                              amount:
                                                Number(e.target.value) || 0,
                                            }
                                          : i
                                      )
                                    )
                                  }
                                  inputProps={{ step: '0.01', min: '0' }}
                                />
                              ) : (
                                (typeof item.amount === 'number'
                                  ? item.amount
                                  : Number(item.amount) || 0
                                ).toFixed(2)
                              )}
                            </TableCell>
                            <TableCell align="right">
                              {item.isEditing ? (
                                <>
                                  <IconButton
                                    size="small"
                                    onClick={() => handleSaveItem(item.id!)}
                                    disabled={isGeneratingBill}
                                  >
                                    <SaveIcon />
                                  </IconButton>
                                  <IconButton
                                    size="small"
                                    onClick={() => handleCancelEdit(item.id!)}
                                    disabled={isGeneratingBill}
                                  >
                                    <CancelIcon />
                                  </IconButton>
                                </>
                              ) : (
                                <>
                                  <IconButton
                                    size="small"
                                    onClick={() =>
                                      item.id && handleEditItem(item.id)
                                    }
                                    disabled={
                                      !!editingItemId ||
                                      !item.id ||
                                      isGeneratingBill
                                    }
                                  >
                                    <EditIcon />
                                  </IconButton>
                                  <IconButton
                                    size="small"
                                    onClick={() => handleRemoveItem(item.id!)}
                                    disabled={
                                      !!editingItemId || isGeneratingBill
                                    }
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                </>
                              )}
                            </TableCell>
                          </TableRow>
                        );
                      })
                    )}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Summary */}
              <Box mt={4} display="flex" justifyContent="flex-end">
                <Box sx={{ width: 300 }}>
                  <Box display="flex" justifyContent="space-between" mb={1}>
                    <Typography variant="subtitle1">Total:</Typography>
                    <Typography variant="subtitle1">
                      ₹{totalAmount.toFixed(2)}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Paper>
          </Grid>

          {/* Action Buttons */}
          <Box display="flex" justifyContent="flex-end" gap={2} mt={3}>
            <Button
              variant="outlined"
              color="primary"
              onClick={() => navigate(-1)}
              disabled={isSaving || isGeneratingBill}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSaveContract}
              disabled={isSaving || !!editingItemId || isGeneratingBill}
              startIcon={isSaving ? <CircularProgress size={20} /> : null}
            >
              {isSaving ? 'Saving...' : 'Update Contract'}
            </Button>
            <Button
              variant="contained"
              color="success"
              onClick={handleGenerateBill}
              disabled={
                isSaving ||
                isGeneratingBill ||
                contractItems.length === 0 ||
                !!editingItemId
              }
              startIcon={
                isGeneratingBill ? <CircularProgress size={20} /> : null
              }
            >
              {isGeneratingBill ? 'Generating...' : 'Generate Bill'}
            </Button>
          </Box>
        </>
      )}
    </Grid>
  );
};

export default EditContract;

import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import EditIcon from '@mui/icons-material/Edit';
import { Box, Button, Grid, Paper, TextField, Typography } from '@mui/material';

import MaterialTable from '@material-table/core';

import PageTitle from '@components/PageTitle';

import { formatDate } from '../../helpers/dateFormat';
import { useAddAdditional } from '../../hooks/additional';
import {
  useReadBills,
  useUpdateBillByHid,
  useUpdateBillRegByBillNo,
} from '../../hooks/bill';
import { useFilterHireChart, useUpdateHireChart } from '../../hooks/hirechart';

const DirectBillDetail = () => {
  const { enqueueSnackbar } = useSnackbar();
  const { mutate: updateBillRegByBillNoMutate } = useUpdateBillRegByBillNo();
  const { mutate: addAdditionalMutate } = useAddAdditional();
  const { mutate: updateBillByHidMutate } = useUpdateBillByHid();
  const { mutate: updateHirechartMutate } = useUpdateHireChart();

  const [discountAmount, setDiscountAmount] = useState('0');
  const [additionalAmount, setAdditionalAmount] = useState('0');
  const [additionalParticular, setAdditionalParticular] = useState('0');

  const [hirecharts, setHirecharts] = useState<any>();
  const [selectedHirecharts, setSelectedHirecharts] = useState<any>([]);

  const { id } = useParams();
  const location = useLocation();

  const [isLoading, setIsLoading] = useState(true);

  const { data: billList } = useReadBills();

  const { mutate: filterHireChartMutate, isPending } = useFilterHireChart();

  // Load initial data
  useEffect(() => {
    const loadHirecharts = async () => {
      try {
        const storedData = localStorage.getItem('hirechartDetails');
        if (!storedData) {
          setIsLoading(false);
          return;
        }

        const parsedData = JSON.parse(storedData);

        filterHireChartMutate(
          {},
          {
            onSuccess: (res: any) => {
              if (!res?.data) {
                setIsLoading(false);
                return;
              }

              const modifiedData = parsedData.map((h: any) => {
                const bill: any = billList?.find(
                  b => Number(b.hid) === Number(h.originalId)
                );

                const hirechart = res?.data?.find(
                  (hc: any) => hc.id === h.originalId
                );

                return {
                  id: h.originalId,
                  hirechart,
                  bill,
                };
              });

              if (modifiedData && modifiedData.length) {
                setHirecharts(modifiedData);
              }
              setIsLoading(false);
            },
            onError: () => {
              enqueueSnackbar('Failed to load hirechart data', {
                variant: 'error',
              });
              setIsLoading(false);
            },
          }
        );
      } catch (error) {
        enqueueSnackbar('Error processing data', { variant: 'error' });
        setIsLoading(false);
      }
    };

    if (billList && !hirecharts) {
      loadHirecharts();
    }
  }, [billList, filterHireChartMutate, enqueueSnackbar, hirecharts]);

  // Update bills when hirecharts are loaded
  useEffect(() => {
    if (hirecharts) {
      hirecharts.forEach((h: any) => {
        updateBillByHidMutate({
          hid: h.id,
        });
      });
    }
  }, [hirecharts, updateBillByHidMutate]);

  const navigate = useNavigate();

  const setSelectedRows = (rowsData: any) => {
    setSelectedHirecharts(rowsData);
  };

  const handleMakeBill = () => {
    if (!selectedHirecharts || selectedHirecharts.length === 0) {
      enqueueSnackbar('Please select hire first', { variant: 'warning' });
      return;
    }

    const hirechartToDisplay = selectedHirecharts.map((h: any) =>
      hirecharts?.find((hc: any) => hc.id === h.hirechart.id)
    );

    updateHirechartMutate(
      {
        id: hirechartToDisplay[0].id,
        bill: 1,
      },
      {
        onSuccess: () => {
          localStorage.setItem(
            'hirecharts',
            JSON.stringify(hirechartToDisplay)
          );
          navigate(`/bill/view`, { state: { from: location.pathname } });
        },
        onError: () => {
          enqueueSnackbar('Failed to update hirechart', { variant: 'error' });
        },
      }
    );
  };

  const handleEdit = (rowData: any) => {
    if (rowData) {
      navigate(`/newBill/${rowData.id}/edit`);
    }
  };

  const updateAdditionalDetails = () => {
    addAdditionalMutate(
      {
        billNo: id,
        particular: additionalParticular,
        amount: additionalAmount,
      },
      {
        onSuccess: () => {
          enqueueSnackbar('Content Updated', { variant: 'info' });
        },
        onError: () => {
          enqueueSnackbar('Failed to Update Content', { variant: 'error' });
        },
      }
    );
  };

  const updateDiscountDetails = () => {
    updateBillRegByBillNoMutate(
      { billNum: id, discount: discountAmount },
      {
        onSuccess: () => {
          enqueueSnackbar('Content Updated', { variant: 'info' });
        },
        onError: () => {
          enqueueSnackbar('Failed to Update year', { variant: 'error' });
        },
      }
    );
  };

  const columns: any = [
    {
      title: 'Date',
      field: 'date',
      defaultSort: 'desc',
      render: (rowData: any) => `${formatDate(rowData?.hirechart?.date)} `,
    },
    {
      title: 'Company',
      field: 'company',
      render: (rowData: any) => `${rowData?.hirechart?.company} `,
    },
    {
      title: 'Vehicle Type',
      field: 'vtype',
      render: (rowData: any) => `${rowData?.hirechart?.vtype} `,
    },
    {
      title: 'Vehicle No',
      field: 'vehicleno',
      render: (rowData: any) => `${rowData?.hirechart?.vno || ''} `,
    },
    {
      title: 'Particular',
      render: (rowData: any) =>
        rowData.hirechart?.particulars_type
          ? `${rowData.hirechart?.particulars} ${rowData.hirechart?.particulars_type}`
          : rowData.hirechart?.particulars || '',
    },
    {
      title: '8 Hrs / 100 KMs',
      field: 'kms',
      render: (rowData: any) => `${rowData?.bill?.rate || 0} `,
    },
    {
      title: 'Ex Kms',
      field: 'xkms',
      render: (rowData: any) => `${rowData?.bill?.xkms || 0} `,
    },
    {
      title: 'Rate',
      field: 'rate',
      render: (rowData: any) => `${rowData?.bill?.kms || 0} `,
    },
    {
      title: 'Tot',
      field: 'tot1',
      render: (rowData: any) => `${rowData?.bill?.tot1 || 0} `,
    },
    {
      title: 'Ex HRs',
      field: 'xhrs',
      render: (rowData: any) => `${rowData?.bill?.xhrs || 0} `,
    },
    {
      title: 'Wait Ch',
      field: 'wchr',
      render: (rowData: any) => `${rowData?.bill?.wchr || 0} `,
    },
    {
      title: 'Tot',
      field: 'tot2',
      render: (rowData: any) => `${rowData?.bill?.tot2 || 0} `,
    },
    {
      title: 'Early',
      field: 'early',
      render: (rowData: any) => `${rowData?.bill?.early || 0} `,
    },
    {
      title: 'Early Morning',
      field: 'early_morning',
      render: (rowData: any) => `${rowData?.bill?.early_morning || 0} `,
    },
    {
      title: 'Late',
      field: 'late',
      render: (rowData: any) => `${rowData?.bill?.late || 0} `,
    },
    {
      title: 'Nite',
      field: 'onite',
      render: (rowData: any) => `${rowData?.bill?.onite || 0} `,
    },
    {
      title: 'Extra',
      field: 'toll',
      render: (rowData: any) => `${rowData?.bill?.toll || 0} `,
    },
    {
      title: 'Total',
      field: 'tot3',
      render: (rowData: any) => `${rowData?.bill?.tot3 || 0} `,
    },
    {
      title: 'hid',
      field: 'hid',
      render: (rowData: any) => `${rowData?.bill?.hid} `,
      hidden: true,
    },
  ];

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle text="New Bill" />
      </Grid>
      <Grid item xs container spacing={2}>
        <Paper elevation={2} sx={{ width: '100%' }}>
          <Grid
            container
            item
            xs={4}
            sm={2}
            alignSelf="flex-start"
            direction="row"
            paddingTop={4}
            paddingLeft={2}
          >
            <Grid item>
              <Button
                variant="contained"
                color="primary"
                onClick={handleMakeBill}
              >
                Make Bill
              </Button>
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <MaterialTable
              title=""
              columns={columns}
              data={hirecharts || []}
              isLoading={isLoading || isPending}
              options={{
                draggable: false,
                paging: false,
                search: true,
                tableLayout: 'fixed',
                searchFieldVariant: 'outlined',
                selection: true,
              }}
              style={{
                minHeight: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
              components={{
                Container: props => <Paper elevation={0} {...props} />,
                Toolbar: () => <div style={{ margin: '10px' }}> </div>,
              }}
              onSelectionChange={rows => setSelectedRows(rows)}
              actions={[
                {
                  icon: EditIcon,
                  tooltip: 'Edit',
                  position: 'row',
                  onClick: (_, rowData) => handleEdit(rowData),
                },
              ]}
            />
          </Grid>

          <Box>
            <Box sx={{ mt: 2, mb: 2 }} marginLeft={2}>
              <Typography style={{ marginLeft: '4px' }} variant="h6">
                Add Additional Charges
              </Typography>
              <TextField
                id="additionalParticular"
                value={additionalParticular}
                onChange={e => setAdditionalParticular(e.target.value)}
                label="Particular"
                variant="outlined"
                style={{ marginLeft: '12px' }}
              />
              <TextField
                id="additionalAmount"
                value={additionalAmount}
                onChange={e => setAdditionalAmount(e.target.value)}
                label="Amount"
                variant="outlined"
                style={{ marginLeft: '12px' }}
              />
              <Button
                style={{ marginLeft: '12px' }}
                variant="contained"
                onClick={updateAdditionalDetails}
              >
                Add
              </Button>
            </Box>
          </Box>
          <Box>
            <Box sx={{ mt: 2, mb: 2 }} marginLeft={2}>
              <Typography style={{ marginLeft: '4px' }} variant="h6">
                Discount
              </Typography>
              <TextField
                id="amount"
                value={discountAmount}
                onChange={e => setDiscountAmount(e.target.value)}
                label="Amount"
                variant="outlined"
                style={{ marginLeft: '12px' }}
              />
              <Button
                style={{ marginLeft: '12px' }}
                variant="contained"
                onClick={updateDiscountDetails}
              >
                Add
              </Button>
            </Box>
          </Box>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default DirectBillDetail;
